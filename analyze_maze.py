import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def read_excel_file(file_path):
    """读取Excel文件并显示基本信息"""
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"Excel文件包含的工作表: {excel_file.sheet_names}")
        
        all_data = {}
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            all_data[sheet_name] = df
            print(f"\n=== 工作表: {sheet_name} ===")
            print(f"数据形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            print("\n前几行数据:")
            print(df.head())
            print("\n数据类型:")
            print(df.dtypes)
            
        return all_data
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def analyze_maze_data(data_dict):
    """分析迷宫数据"""
    for sheet_name, df in data_dict.items():
        print(f"\n=== 分析工作表: {sheet_name} ===")
        
        # 检查是否包含方向信息
        direction_columns = []
        for col in df.columns:
            col_str = str(col).lower()
            if any(keyword in col_str for keyword in ['方向', '走', '左', '右', '前', '后', '上', '下']):
                direction_columns.append(col)
        
        if direction_columns:
            print(f"发现方向相关列: {direction_columns}")
            
        # 检查是否包含坐标信息
        coord_columns = []
        for col in df.columns:
            col_str = str(col).lower()
            if any(keyword in col_str for keyword in ['x', 'y', '坐标', '位置', '行', '列']):
                coord_columns.append(col)
                
        if coord_columns:
            print(f"发现坐标相关列: {coord_columns}")
            
        # 显示唯一值
        for col in df.columns:
            unique_values = df[col].unique()
            if len(unique_values) <= 20:  # 只显示唯一值较少的列
                print(f"\n列 '{col}' 的唯一值: {unique_values}")
        
        # 检查是否有路径序列
        if len(df) > 1:
            print(f"\n数据包含 {len(df)} 行，可能是路径序列")

def visualize_maze_path(data_dict):
    """可视化迷宫路径"""
    for sheet_name, df in data_dict.items():
        print(f"\n=== 可视化工作表: {sheet_name} ===")
        
        # 尝试找到可以可视化的数据
        if len(df.columns) >= 2:
            # 创建图表
            fig, axes = plt.subplots(1, 2, figsize=(15, 6))
            
            # 第一个图：数据概览
            if df.select_dtypes(include=[np.number]).shape[1] > 0:
                numeric_df = df.select_dtypes(include=[np.number])
                if len(numeric_df.columns) >= 2:
                    axes[0].scatter(numeric_df.iloc[:, 0], numeric_df.iloc[:, 1])
                    axes[0].plot(numeric_df.iloc[:, 0], numeric_df.iloc[:, 1], 'r-', alpha=0.5)
                    axes[0].set_title(f'{sheet_name} - 数值数据路径图')
                    axes[0].set_xlabel(numeric_df.columns[0])
                    axes[0].set_ylabel(numeric_df.columns[1])
                    axes[0].grid(True)
            
            # 第二个图：数据分布
            if len(df) > 1:
                axes[1].plot(range(len(df)), df.index, 'bo-')
                axes[1].set_title(f'{sheet_name} - 步骤序列')
                axes[1].set_xlabel('步骤编号')
                axes[1].set_ylabel('数据行索引')
                axes[1].grid(True)
            
            plt.tight_layout()
            plt.savefig(f'maze_analysis_{sheet_name}.png', dpi=300, bbox_inches='tight')
            plt.show()

def extract_emei_maze_guide(df):
    """专门提取峨眉山迷宫攻略"""
    print("\n=== 峨眉山迷宫攻略详细分析 ===")

    # 找到峨眉山相关的行
    emei_rows = []
    emei_start_idx = None
    emei_end_idx = None

    for idx, row in df.iterrows():
        if pd.notna(row.iloc[0]) and '峨眉山' in str(row.iloc[0]):
            emei_start_idx = idx
            print(f"找到峨眉山迷宫开始位置：第{idx+1}行")
            break

    if emei_start_idx is not None:
        # 找到峨眉山部分的结束位置
        for idx in range(emei_start_idx + 1, len(df)):
            if pd.notna(df.iloc[idx, 0]) and df.iloc[idx, 0] not in ['', ' ', '峨眉山']:
                if '山' in str(df.iloc[idx, 0]) or '洞' in str(df.iloc[idx, 0]) or '林' in str(df.iloc[idx, 0]):
                    emei_end_idx = idx
                    break

        if emei_end_idx is None:
            emei_end_idx = len(df)

        print(f"峨眉山迷宫数据范围：第{emei_start_idx+1}行到第{emei_end_idx}行")

        # 提取峨眉山迷宫数据
        emei_data = df.iloc[emei_start_idx:emei_end_idx].copy()

        print("\n峨眉山迷宫原始数据：")
        for idx, row in emei_data.iterrows():
            row_data = [str(cell) if pd.notna(cell) else '' for cell in row]
            if any(cell.strip() for cell in row_data):  # 只显示非空行
                print(f"第{idx+1}行: {row_data}")

        # 解析路径指引
        print("\n=== 峨眉山迷宫路径解析 ===")
        path_instructions = []

        for idx, row in emei_data.iterrows():
            step_info = {}
            row_data = [str(cell) if pd.notna(cell) else '' for cell in row]

            # 检查是否是步骤行（包含数字）
            if len(row_data) > 1 and row_data[1].strip().isdigit():
                step_num = int(row_data[1].strip())
                step_info['步骤'] = step_num

                # 解析方向指引
                directions = {}
                for i, cell in enumerate(row_data[2:], 2):
                    if cell and cell.strip():
                        if '前：' in cell:
                            directions['前'] = cell.replace('前：', '').strip()
                        elif '左：' in cell:
                            directions['左'] = cell.replace('左：', '').strip()
                        elif '右：' in cell:
                            directions['右'] = cell.replace('右：', '').strip()
                        elif '后：' in cell:
                            directions['后'] = cell.replace('后：', '').strip()
                        elif cell in ['←', '→', '↑', '↓']:
                            directions['推荐方向'] = cell
                        elif '撞墙' in cell:
                            directions['注意'] = '撞墙'

                if directions:
                    step_info['方向选择'] = directions
                    path_instructions.append(step_info)

        # 显示解析后的路径
        if path_instructions:
            print("\n🗺️ 峨眉山迷宫完整攻略路径：")
            print("=" * 50)
            for instruction in path_instructions:
                step = instruction['步骤']
                directions = instruction['方向选择']

                print(f"\n📍 第{step}步：")
                for direction, destination in directions.items():
                    if direction == '推荐方向':
                        print(f"   ✅ 推荐走向：{destination}")
                    elif direction == '注意':
                        print(f"   ⚠️  {destination}")
                    else:
                        print(f"   {direction}走 → {destination}")

        return path_instructions
    else:
        print("❌ 未找到峨眉山迷宫数据")
        return []

def create_maze_guide_visualization(path_instructions):
    """创建迷宫攻略的可视化图表"""
    if not path_instructions:
        return

    # 创建路径流程图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 左图：步骤流程
    steps = [inst['步骤'] for inst in path_instructions]
    ax1.plot(steps, range(len(steps)), 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('迷宫步骤编号')
    ax1.set_ylabel('路径序列')
    ax1.set_title('峨眉山迷宫步骤流程图')
    ax1.grid(True, alpha=0.3)
    ax1.invert_yaxis()

    # 右图：方向统计
    all_directions = []
    for inst in path_instructions:
        directions = inst.get('方向选择', {})
        for direction in directions.keys():
            if direction not in ['推荐方向', '注意']:
                all_directions.append(direction)

    if all_directions:
        direction_counts = pd.Series(all_directions).value_counts()
        ax2.pie(direction_counts.values, labels=direction_counts.index, autopct='%1.1f%%')
        ax2.set_title('峨眉山迷宫方向选择分布')

    plt.tight_layout()
    plt.savefig('emei_maze_guide.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    file_path = "迷宫攻略.xlsx"

    if not Path(file_path).exists():
        print(f"文件 {file_path} 不存在！")
        return

    print("🎮 开始读取和分析迷宫攻略Excel文件...")

    # 读取Excel文件
    data = read_excel_file(file_path)

    if data and 'Sheet1' in data:
        df = data['Sheet1']

        # 专门分析峨眉山迷宫
        emei_instructions = extract_emei_maze_guide(df)

        # 创建可视化
        if emei_instructions:
            create_maze_guide_visualization(emei_instructions)

        print("\n" + "="*60)
        print("🎯 峨眉山迷宫攻略总结")
        print("="*60)
        print("根据你提供的游戏截图，这是峨眉山迷宫的入口界面。")
        print("游戏中有四个选项：向前走、向左走、向右走、返回")
        print("\n📋 使用攻略的方法：")
        print("1. 按照Excel表格中的步骤编号顺序进行")
        print("2. 每一步都有多个方向选择，选择正确的方向")
        print("3. 注意避开'撞墙'的方向")
        print("4. 跟随推荐方向标记（箭头符号）")

        if emei_instructions:
            print(f"\n📊 峨眉山迷宫共有 {len(emei_instructions)} 个关键步骤")
            print("详细的每一步指引已在上方显示 ⬆️")

if __name__ == "__main__":
    main()
