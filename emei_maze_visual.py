import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_emei_maze_map():
    """创建峨眉山迷宫的可视化地图"""
    
    # 根据Excel数据构建迷宫地图
    maze_data = {
        1: {'前': 1, '左': 2, '右': 1, '位置': (2, 3)},
        2: {'前': 1, '左': 3, '右': '2~', '位置': (1, 2)},
        3: {'前': 3, '左': 2, '右': 4, '位置': (1, 1)},
        4: {'前': 2, '左': 3, '右': '峨眉金顶', '位置': (2, 1)}
    }
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 左图：迷宫地图
    ax1.set_xlim(0, 4)
    ax1.set_ylim(0, 4)
    ax1.set_aspect('equal')
    ax1.set_title('峨眉山迷宫地图布局', fontsize=16, fontweight='bold')
    
    # 绘制迷宫节点
    colors = ['lightblue', 'lightgreen', 'lightyellow', 'lightcoral']
    for step, info in maze_data.items():
        x, y = info['位置']
        circle = patches.Circle((x, y), 0.3, facecolor=colors[step-1], edgecolor='black', linewidth=2)
        ax1.add_patch(circle)
        ax1.text(x, y, f'{step}', ha='center', va='center', fontsize=14, fontweight='bold')
    
    # 绘制连接线和方向标记
    for step, info in maze_data.items():
        x, y = info['位置']
        
        # 前方向
        if isinstance(info['前'], int) and info['前'] in maze_data:
            target_x, target_y = maze_data[info['前']]['位置']
            ax1.annotate('', xy=(target_x, target_y), xytext=(x, y+0.3),
                        arrowprops=dict(arrowstyle='->', color='blue', lw=2))
            ax1.text(x, y+0.5, '前', ha='center', va='center', color='blue', fontweight='bold')
        
        # 左方向
        if isinstance(info['左'], int) and info['左'] in maze_data:
            target_x, target_y = maze_data[info['左']]['位置']
            ax1.annotate('', xy=(target_x, target_y), xytext=(x-0.3, y),
                        arrowprops=dict(arrowstyle='->', color='green', lw=2))
            ax1.text(x-0.5, y, '左', ha='center', va='center', color='green', fontweight='bold')
        
        # 右方向
        if info['右'] == '峨眉金顶':
            ax1.text(x+0.7, y, '🏔️峨眉金顶', ha='left', va='center', color='red', fontsize=12, fontweight='bold')
            ax1.annotate('', xy=(x+0.6, y), xytext=(x+0.3, y),
                        arrowprops=dict(arrowstyle='->', color='red', lw=3))
        elif isinstance(info['右'], int) and info['右'] in maze_data:
            target_x, target_y = maze_data[info['右']]['位置']
            ax1.annotate('', xy=(target_x, target_y), xytext=(x+0.3, y),
                        arrowprops=dict(arrowstyle='->', color='orange', lw=2))
            ax1.text(x+0.5, y, '右', ha='center', va='center', color='orange', fontweight='bold')
    
    # 标记入口
    ax1.text(2, 3.7, '🚪 迷宫入口', ha='center', va='center', fontsize=14, fontweight='bold', 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('X坐标')
    ax1.set_ylabel('Y坐标')
    
    # 右图：通关路径指南
    ax2.axis('off')
    ax2.set_title('峨眉山迷宫通关攻略', fontsize=16, fontweight='bold')
    
    guide_text = """
🎯 目标：到达峨眉金顶

📍 最佳通关路径：
第1步 → 第2步 → 第3步 → 第4步 → 峨眉金顶

🗺️ 详细路线说明：

第1步（起点）：
• 前走 → 回到第1步（循环）
• 左走 → 到第2步 ✅
• 右走 → 回到第1步（循环）
推荐：选择"左走"

第2步：
• 前走 → 回到第1步
• 左走 → 到第3步 ✅
• 右走 → 到第2步变体
推荐：选择"左走"

第3步：
• 前走 → 回到第3步（循环）
• 左走 → 回到第2步
• 右走 → 到第4步 ✅
推荐：选择"右走"

第4步：
• 前走 → 回到第2步
• 左走 → 回到第3步
• 右走 → 峨眉金顶 🏆
推荐：选择"右走"

⚠️ 注意事项：
• 避免循环路径
• 按照推荐方向走最快
• 可能遇到神秘商人获得淫羊草
"""
    
    ax2.text(0.05, 0.95, guide_text, transform=ax2.transAxes, fontsize=11,
             verticalalignment='top', horizontalalignment='left',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('emei_maze_complete_guide.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_step_by_step_guide():
    """创建分步骤的详细指南"""
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('峨眉山迷宫分步骤攻略', fontsize=18, fontweight='bold')
    
    steps_info = [
        {
            'title': '第1步：迷宫入口',
            'choices': ['前走→第1步(循环)', '左走→第2步✅', '右走→第1步(循环)'],
            'recommendation': '选择：向左走',
            'color': 'lightblue'
        },
        {
            'title': '第2步：第一个分岔',
            'choices': ['前走→第1步', '左走→第3步✅', '右走→第2步变体'],
            'recommendation': '选择：向左走',
            'color': 'lightgreen'
        },
        {
            'title': '第3步：关键分岔',
            'choices': ['前走→第3步(循环)', '左走→第2步', '右走→第4步✅'],
            'recommendation': '选择：向右走',
            'color': 'lightyellow'
        },
        {
            'title': '第4步：最后一步',
            'choices': ['前走→第2步', '左走→第3步', '右走→峨眉金顶🏆'],
            'recommendation': '选择：向右走',
            'color': 'lightcoral'
        }
    ]
    
    for i, (ax, step_info) in enumerate(zip(axes.flat, steps_info)):
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.set_title(step_info['title'], fontsize=14, fontweight='bold')
        
        # 绘制选择框
        for j, choice in enumerate(step_info['choices']):
            y_pos = 8 - j * 2
            if '✅' in choice:
                rect = patches.Rectangle((1, y_pos-0.5), 8, 1, 
                                       facecolor='lightgreen', edgecolor='green', linewidth=2)
            else:
                rect = patches.Rectangle((1, y_pos-0.5), 8, 1, 
                                       facecolor='lightgray', edgecolor='gray', linewidth=1)
            ax.add_patch(rect)
            ax.text(5, y_pos, choice.replace('✅', ''), ha='center', va='center', fontsize=10)
        
        # 添加推荐
        ax.text(5, 1, step_info['recommendation'], ha='center', va='center', 
                fontsize=12, fontweight='bold', color='red',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        
        ax.set_xticks([])
        ax.set_yticks([])
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_visible(False)
        ax.spines['left'].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('emei_maze_step_guide.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("🎮 创建峨眉山迷宫可视化攻略...")
    create_emei_maze_map()
    create_step_by_step_guide()
    print("✅ 可视化攻略创建完成！")
